/* [next]/internal/font/google/poppins_b6e252b8.module.css [app-client] (css) */
@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("../media/pxiGyp8kv8JHgFVrLPTucXtAOvWDSHFF.477e9f30.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("../media/pxiGyp8kv8JHgFVrLPTufntAOvWDSHFF.f058f0cf.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("../media/pxiGyp8kv8JHgFVrLPTucHtAOvWDSA.p.65d31cf5.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLFj_Z11lFd2JQEl8qw.796e930e.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLFj_Z1JlFd2JQEl8qw.a000242e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLFj_Z1xlFd2JQEk.p.79a0f3e8.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLDz8Z11lFd2JQEl8qw.07792bab.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLDz8Z1JlFd2JQEl8qw.8da2481e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLDz8Z1xlFd2JQEk.p.11d83409.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/pxiEyp8kv8JHgFVrJJbecnFHGPezSQ.f7608c0b.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/pxiEyp8kv8JHgFVrJJnecnFHGPezSQ.bfdffbb8.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/pxiEyp8kv8JHgFVrJJfecnFHGPc.p.c336a9d1.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLGT9Z11lFd2JQEl8qw.a02d710f.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLGT9Z1JlFd2JQEl8qw.87c6fae1.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLGT9Z1xlFd2JQEk.p.ab5dbd7e.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLEj6Z11lFd2JQEl8qw.55ef469a.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLEj6Z1JlFd2JQEl8qw.cc58d8dc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLEj6Z1xlFd2JQEk.p.dd0b3887.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLCz7Z11lFd2JQEl8qw.45055564.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLCz7Z1JlFd2JQEl8qw.0c442f40.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.p.d95035ce.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLDD4Z11lFd2JQEl8qw.8e4a632a.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLDD4Z1JlFd2JQEl8qw.65d48da8.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLDD4Z1xlFd2JQEk.p.d66bbad7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLBT5Z11lFd2JQEl8qw.9d1b71df.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLBT5Z1JlFd2JQEl8qw.d42316f9.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/pxiByp8kv8JHgFVrLBT5Z1xlFd2JQEk.p.c194becc.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

.poppins_b6e252b8-module__fclFkG__className {
  font-family: Poppins, Helvetica, Arial, sans-serif;
  font-style: normal;
}

.poppins_b6e252b8-module__fclFkG__variable {
  --font-poppins: "Poppins", Helvetica, Arial, sans-serif;
}


/* [project]/app/spacegrotesk_5a4c50b3.module.css [app-client] (css) */
@font-face {
  font-family: spaceGrotesk;
  src: url("../media/SpaceGroteskVF-s.p.d5f07973.ttf") format("truetype");
  font-display: swap;
  font-weight: 300 400 500 600 700;
}

@font-face {
  font-family: spaceGrotesk Fallback;
  src: local(Arial);
  ascent-override: 88.78%;
  descent-override: 26.34%;
  line-gap-override: 0.0%;
  size-adjust: 110.84%;
}

.spacegrotesk_5a4c50b3-module__ecgvsW__className {
  font-family: spaceGrotesk, spaceGrotesk Fallback;
}

.spacegrotesk_5a4c50b3-module__ecgvsW__variable {
  --font-space-grotesk: "spaceGrotesk", "spaceGrotesk Fallback";
}


/* [project]/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-orange-500: oklch(70.5% .213 47.604);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-700: oklch(55.4% .135 66.442);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-indigo-50: oklch(96.2% .018 272.314);
    --color-purple-50: oklch(97.7% .014 308.299);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-700: oklch(49.6% .265 301.924);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-md: 28rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-6xl: 72rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --tracking-widest: .1em;
    --leading-relaxed: 1.625;
    --radius-sm: calc(var(--radius)  - 4px);
    --radius-md: calc(var(--radius)  - 2px);
    --radius-lg: var(--radius);
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-4xl: 2rem;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --aspect-video: 16 / 9;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --radius: .5rem;
    --color-primary: #6938ef;
    --color-secondary: #946eff;
    --font-poppins: var(--font-poppins);
    --font-space-grotesk: var(--font-space-grotesk);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  *, :after, :before, ::backdrop, ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }

  :root {
    --radius: .5rem;
  }
}

@layer components;

@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-6 {
    top: calc(var(--spacing) * 6);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-\[17px\] {
    left: 17px;
  }

  .z-10 {
    z-index: 10;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-7 {
    margin-top: calc(var(--spacing) * 7);
  }

  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }

  .mt-11 {
    margin-top: calc(var(--spacing) * 11);
  }

  .mr-2\.5 {
    margin-right: calc(var(--spacing) * 2.5);
  }

  .mr-10 {
    margin-right: calc(var(--spacing) * 10);
  }

  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .ml-auto {
    margin-left: auto;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-flex {
    display: inline-flex;
  }

  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-28 {
    height: calc(var(--spacing) * 28);
  }

  .h-\[1\.2rem\] {
    height: 1.2rem;
  }

  .h-\[300px\] {
    height: 300px;
  }

  .h-\[488px\] {
    height: 488px;
  }

  .h-\[550px\] {
    height: 550px;
  }

  .h-fit {
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .h-screen {
    height: 100vh;
  }

  .max-h-\(--radix-dropdown-menu-content-available-height\) {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  .min-h-12 {
    min-height: calc(var(--spacing) * 12);
  }

  .min-h-\[600px\] {
    min-height: 600px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-30 {
    width: calc(var(--spacing) * 30);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-54 {
    width: calc(var(--spacing) * 54);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-\[1\.2rem\] {
    width: 1.2rem;
  }

  .w-\[3px\] {
    width: 3px;
  }

  .w-\[265px\] {
    width: 265px;
  }

  .w-\[300px\] {
    width: 300px;
  }

  .w-full {
    width: 100%;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-\[300px\] {
    max-width: 300px;
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-full {
    min-width: 100%;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .origin-\(--radix-dropdown-menu-content-transform-origin\) {
    transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rotate-0 {
    rotate: none;
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize-none {
    resize: none;
  }

  .list-inside {
    list-style-position: inside;
  }

  .list-decimal {
    list-style-type: decimal;
  }

  .list-disc {
    list-style-type: disc;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2.5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: var(--radius);
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-4xl {
    border-radius: var(--radius-4xl);
  }

  .rounded-\[10px\] {
    border-radius: 10px;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-\[\#aba6bb\] {
    border-color: #aba6bb;
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-400 {
    border-color: var(--color-gray-400);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-yellow-400 {
    border-color: var(--color-yellow-400);
  }

  .bg-\[\#6938EF\] {
    background-color: #6938ef;
  }

  .bg-\[\#C7ACF5\] {
    background-color: #c7acf5;
  }

  .bg-\[\#CCFFB1\] {
    background-color: #ccffb1;
  }

  .bg-\[\#F4F1FE\] {
    background-color: #f4f1fe;
  }

  .bg-\[\#dddae5\] {
    background-color: #dddae5;
  }

  .bg-\[\#dfdbe9\] {
    background-color: #dfdbe9;
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }

  .bg-gray-300\/50 {
    background-color: #d1d5dc80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-300\/50 {
      background-color: color-mix(in oklab, var(--color-gray-300) 50%, transparent);
    }
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }

  .bg-primary {
    background-color: var(--color-primary);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-secondary {
    background-color: var(--color-secondary);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-200 {
    background-color: var(--color-yellow-200);
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-\[\#6938EF\] {
    --tw-gradient-from: #6938ef;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-indigo-50 {
    --tw-gradient-from: var(--color-indigo-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white {
    --tw-gradient-from: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#8b5cf6\] {
    --tw-gradient-to: #8b5cf6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#6938EF\] {
    --tw-gradient-to: #6938ef;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-50 {
    --tw-gradient-to: var(--color-purple-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .bg-cover {
    background-size: cover;
  }

  .fill-current {
    fill: currentColor;
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-\[3px\] {
    padding-left: 3px;
  }

  .text-center {
    text-align: center;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[11px\] {
    font-size: 11px;
  }

  .leading-7 {
    --tw-leading: calc(var(--spacing) * 7);
    line-height: calc(var(--spacing) * 7);
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-\[\#1E1E1E\] {
    color: #1e1e1e;
  }

  .text-\[\#6938EF\] {
    color: #6938ef;
  }

  .text-\[\#38383a\] {
    color: #38383a;
  }

  .text-\[\#616161\] {
    color: #616161;
  }

  .text-\[\#aba6bb\] {
    color: #aba6bb;
  }

  .text-black {
    color: var(--color-black);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-primary {
    color: var(--color-primary);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-700 {
    color: var(--color-purple-700);
  }

  .text-red-400 {
    color: var(--color-red-400);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-700 {
    color: var(--color-yellow-700);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .lowercase {
    text-transform: lowercase;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .placeholder-gray-400::placeholder {
    color: var(--color-gray-400);
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .paused {
    animation-play-state: paused;
  }

  @media (hover: hover) {
    .group-hover\:translate-x-1:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .selection\:bg-primary ::selection, .selection\:bg-primary::selection {
    background-color: var(--color-primary);
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  @media (hover: hover) {
    .hover\:bg-\[\#5a2fd8\]:hover {
      background-color: #5a2fd8;
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: #6938efe6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: #946effcc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--color-secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-600:hover {
      color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-800:hover {
      color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-600:hover {
      color: var(--color-red-600);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  .focus\:border-\[\#6938EF\]:focus {
    border-color: #6938ef;
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-\[\#6938EF\]\/20:focus {
    --tw-ring-color: oklab(52.2786% .0698801 -.241065 / .2);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:bg-gray-300:disabled {
    background-color: var(--color-gray-300);
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
  }

  .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: calc(2 * var(--spacing));
  }

  .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: calc(2 * var(--spacing));
  }

  .data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .data-\[state\=open\]\:animate-in[data-state="open"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  @media (width >= 40rem) {
    .sm\:mb-5 {
      margin-bottom: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:inline {
      display: inline;
    }
  }

  @media (width >= 40rem) {
    .sm\:h-5 {
      height: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-6 {
      height: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-8 {
      height: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-5 {
      width: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-6 {
      width: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-7 {
      width: calc(var(--spacing) * 7);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-8 {
      width: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-\[200px\] {
      width: 200px;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-\[300px\] {
      width: 300px;
    }
  }

  @media (width >= 40rem) {
    .sm\:min-w-\[520px\] {
      min-width: 520px;
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-1 {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-3 {
      padding-block: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-4 {
      padding-block: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-5 {
      padding-block: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-6 {
      padding-block: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-\[6px\] {
      font-size: 6px;
    }
  }

  @media (width >= 48rem) {
    .md\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:items-start {
      align-items: flex-start;
    }
  }

  @media (width >= 48rem) {
    .md\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:flex {
      display: flex;
    }
  }

  @media (width >= 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-1\/2 {
      width: 50%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[330px\] {
      width: 330px;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 64rem) {
    .lg\:items-start {
      align-items: flex-start;
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-10 {
      gap: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 64rem) {
    .lg\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (width >= 80rem) {
    .xl\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  .dark\:scale-0:is(.dark *) {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .dark\:scale-100:is(.dark *) {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .dark\:-rotate-90:is(.dark *) {
    rotate: -90deg;
  }

  .dark\:rotate-0:is(.dark *) {
    rotate: none;
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
}

@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}

@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}

@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}

@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}

@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

.h1-bold {
  font-size: 34px;
  font-weight: bold;
  line-height: 34px;
}

.h2-semibold {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.h3-medium {
  font-size: 24px;
  font-weight: 500;
  line-height: 20px;
}

.h4-regular {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

.primary-text {
  background: var(--color-primary);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

p {
  color: #38383a;
}

.scrollbar-hidden {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__c15038e8._.css.map*/