"use client";
import React, { useState } from "react";
import InterviewInstructions from "@/components/interview/InterviewInstructions";
import QuestionsPage from "@/components/interview/QuestionsPage";
import InterviewRecording from "@/components/interview/InterviewRecording";
import FinishInterview from "@/components/interview/FinishInterview";
import Analysis from "@/components/interview/Analysis";

type InterviewStep =
  | "instructions"
  | "questions"
  | "recording"
  | "finishInterview"
  | "analysis";

const Interview = () => {
  const [currentStep, setCurrentStep] = useState<InterviewStep>("instructions");

  const renderCurrentComponent = () => {
    switch (currentStep) {
      case "instructions":
        return (
          <InterviewInstructions onNext={() => setCurrentStep("questions")} />
        );
      case "questions":
        return <QuestionsPage onNext={() => setCurrentStep("recording")} />;
      case "recording":
        return (
          <InterviewRecording
            onNext={() => setCurrentStep("finishInterview")}
          />
        );
      case "finishInterview":
        return <FinishInterview onNext={() => setCurrentStep("analysis")} />;

      case "analysis":
        return <Analysis />;
      default:
        return (
          <InterviewInstructions onNext={() => setCurrentStep("questions")} />
        );
    }
  };

  return <div>{renderCurrentComponent()}</div>;
};

export default Interview;
