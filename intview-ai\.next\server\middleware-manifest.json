{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "kx5fv6OaRf1Y5ArHnAo9aEvVgDWGx4JcYen3hw8UgIA=", "__NEXT_PREVIEW_MODE_ID": "cac4d4e7e982387d17cd49f5127911a2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2f451b2b78ff2da6b407e71d89607b75cf27743f42308cbdcebf1a6cffc70fc7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fe9bb59071b86cc26c2e1e147fc3fd609fb7d6bdb5cf84dc152b18bd5dfebfaf"}}}, "sortedMiddleware": ["/"], "functions": {}}